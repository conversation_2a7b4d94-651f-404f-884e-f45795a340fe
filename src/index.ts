import puppeteer from "@cloudflare/puppeteer";

// Browser pool configuration
const BROWSER_POOL_SIZE = 3; // Adjust based on your worker's memory limits
const BROWSER_IDLE_TIMEOUT = 30000; // 30 seconds before closing idle browser
const MAX_PAGES_PER_BROWSER = 5; // Prevent memory leaks

interface PooledBrowser {
    browser: any;
    pages: Set<any>;
    lastUsed: number;
    isClosing: boolean;
}

class BrowserPool {
    private pool: PooledBrowser[] = [];
    private cleanupInterval: any;

    constructor() {
        // Clean up idle browsers every 15 seconds
        this.cleanupInterval = setInterval(() => this.cleanup(), 15000);
    }

    async getBrowser(env: any): Promise<{ browser: any; page: any; release: () => Promise<void> }> {
        // Try to find an available browser
        let pooledBrowser = this.pool.find(pb => 
            !pb.isClosing && 
            pb.pages.size < MAX_PAGES_PER_BROWSER
        );

        // Create new browser if none available and under pool limit
        if (!pooledBrowser && this.pool.length < BROWSER_POOL_SIZE) {
            console.log('Creating new browser for pool');
            const browser = await puppeteer.launch(env.BROWSER);
            pooledBrowser = {
                browser,
                pages: new Set(),
                lastUsed: Date.now(),
                isClosing: false
            };
            this.pool.push(pooledBrowser);
        }

        // If still no browser available, wait and retry (or use oldest)
        if (!pooledBrowser) {
            pooledBrowser = this.pool.reduce((oldest, current) => 
                current.lastUsed < oldest.lastUsed ? current : oldest
            );
        }

        // Create new page
        const page = await pooledBrowser.browser.newPage();
        pooledBrowser.pages.add(page);
        pooledBrowser.lastUsed = Date.now();

        // Return browser, page, and release function
        return {
            browser: pooledBrowser.browser,
            page,
            release: async () => {
                try {
                    await page.close();
                    pooledBrowser!.pages.delete(page);
                    pooledBrowser!.lastUsed = Date.now();
                } catch (error) {
                    console.error('Error releasing page:', error);
                }
            }
        };
    }

    private async cleanup() {
        const now = Date.now();
        const browsersToClose = this.pool.filter(pb => 
            !pb.isClosing && 
            pb.pages.size === 0 && 
            (now - pb.lastUsed) > BROWSER_IDLE_TIMEOUT
        );

        for (const pooledBrowser of browsersToClose) {
            pooledBrowser.isClosing = true;
            try {
                console.log('Closing idle browser');
                await pooledBrowser.browser.close();
                this.pool = this.pool.filter(pb => pb !== pooledBrowser);
            } catch (error) {
                console.error('Error closing browser:', error);
            }
        }
    }

    async closeAll() {
        clearInterval(this.cleanupInterval);
        await Promise.all(
            this.pool.map(async (pb) => {
                try {
                    await pb.browser.close();
                } catch (error) {
                    console.error('Error closing browser in pool:', error);
                }
            })
        );
        this.pool = [];
    }
}

// Global browser pool instance
let browserPool: BrowserPool | null = null;

const testData = {
    "labels": [
        {
            "recipient_name": "Shuri Glover",
            "recipient_address": "Creek Royal Apartments، Accra, Ghana [TESH/SAK-06]",
            "recipient_phone": "122341212",
            "vendor_name": "CALCIUM",
            "vendor_address": "长沙",
            "vendor_phone": "17/07/2025",
            "items": "(AUI08) 115-piece precision screwdriver set [lyy-006]  @ 63 x 1",
            "location_code": "TESH/SAK-06",
            "total_amount": "305.00",
            "tracking_id": "MAGB"
        },
        {
            "recipient_name": "Alhassan Mike",
            "recipient_address": "Roman Ridge, Accra, Ghana [ACC-07]",
            "recipient_phone": "122341212",
            "vendor_name": "CALCIUM",
            "vendor_address": "成都",
            "vendor_phone": "17/07/2025",
            "items": "(AUI08) Car Armrest Box Pad [hxb-b057]  @ SHELF 94 x 1\n (AUI08) Carbon Fibre Stickers [hxb-b067]  @ F-04-03 A 04-02-04 x 1\n (ELG03) Mini 1080p HD Camera A9 [hxb-b010]  @ 68 /  12-04-02 x 1",
            "location_code": "ACC-07",
            "total_amount": "920.00",
            "tracking_id": "YHMA"
        },
        {
            "recipient_name": "Paul Tali",
            "recipient_address": "Tarkwa, Ghana [TAR-25]",
            "recipient_phone": "122341212",
            "vendor_name": "CALCIUM",
            "vendor_address": "Nigeria",
            "vendor_phone": "17/07/2025",
            "items": "(AUI08) DS Foam Cleaner [DFC001]  @ 03-04-02 | 70 x 3",
            "location_code": "TAR-25",
            "total_amount": "300.00",
            "tracking_id": "WPDP"
        },
        {
            "recipient_name": "Abdul Jamal",
            "recipient_address": "Atonsu, Kumasi, Ghana [KUM-26]",
            "recipient_phone": "122341212",
            "vendor_name": "CALCIUM",
            "vendor_address": "Nigeria",
            "vendor_phone": "17/07/2025",
            "items": "(AUI08) DS Foam Cleaner [DFC001]  @ 03-04-02 | 70 x 7",
            "location_code": "KUM-26",
            "total_amount": "499.80",
            "tracking_id": "MF6O"
        },
        {
            "recipient_name": "Kexiah Black",
            "recipient_address": "Ahodwo, Kumasi, Ghana [KUM-26]",
            "recipient_phone": "122341212",
            "vendor_name": "CALCIUM",
            "vendor_address": "岳阳",
            "vendor_phone": "17/07/2025",
            "items": "(AUI08) electric hammer [hjb-B025]  @ 52 x 1\n (AUI08) Welding gun [hjb-B024]  @ 06-02-05| 47 x 1",
            "location_code": "KUM-26",
            "total_amount": "1360.00",
            "tracking_id": "DYAZ"
        },
        {
            "recipient_name": "Mansah Mauw",
            "recipient_address": "Takoradi, Ghana [TAK-24]",
            "recipient_phone": "122341212",
            "vendor_name": "CALCIUM",
            "vendor_address": "岳阳",
            "vendor_phone": "17/07/2025",
            "items": "(AUI08) electric hammer [hjb-B025]  @ 52 x 1",
            "location_code": "TAK-24",
            "total_amount": "985.00",
            "tracking_id": "OZBR"
        },
        {
            "recipient_name": "Alfred Bentu",
            "recipient_address": "Maamobi, Accra, Ghana [ACC-07]",
            "recipient_phone": "122341212",
            "vendor_name": "CALCIUM",
            "vendor_address": "岳阳",
            "vendor_phone": "17/07/2025",
            "items": "(AUI08) electric hammer [hjb-B025]  @ 52 x 1",
            "location_code": "ACC-07",
            "total_amount": "985.00",
            "tracking_id": "GLM2"
        },
        {
            "recipient_name": "Claude Sake",
            "recipient_address": "Takoradi, Ghana [TAK-24]",
            "recipient_phone": "122341212",
            "vendor_name": "CALCIUM",
            "vendor_address": "岳阳",
            "vendor_phone": "17/07/2025",
            "items": "(AUI08) electric hammer [hjb-B025]  @ 52 x 1",
            "location_code": "TAK-24",
            "total_amount": "985.00",
            "tracking_id": "SVAX"
        },
        {
            "recipient_name": "Naru Tobi",
            "recipient_address": "Koforidua, Ghana [KOF-12]",
            "recipient_phone": "122341212",
            "vendor_name": "CALCIUM",
            "vendor_address": "岳阳",
            "vendor_phone": "17/07/2025",
            "items": "(AUI08) electric hammer [hjb-B025]  @ 52 x 1",
            "location_code": "KOF-12",
            "total_amount": "985.00",
            "tracking_id": "KY7R"
        },
        {
            "recipient_name": "Ellias Clue",
            "recipient_address": "ICA Street, Nii Okaiman West, Ghana [ACH-08]",
            "recipient_phone": "122341212",
            "vendor_name": "CALCIUM",
            "vendor_address": "成都",
            "vendor_phone": "17/07/2025",
            "items": "(AUI08) Epoxy Floor Coating-Medium Grey [wkx-b021]  @ 11-02-01 | 18-02-01 | 12-03-05 x 5",
            "location_code": "ACH-08",
            "total_amount": "890.00",
            "tracking_id": "O8I4"
        }
    ],
    "templateType": "vdl-template",
    "dimensions": {
        "height": 6,
        "width": 4,
        "unit": "in"
    },
    "barcodeSource": "recipient_name"
}

export default {
    async fetch(request: any, env: any) {
        // Initialize browser pool if not exists
        if (!browserPool) {
            browserPool = new BrowserPool();
        }

        // // Only handle POST requests for PDF generation
        // if (request.method !== 'POST') {
        //     return new Response('Method not allowed', { status: 405 });
        // }

        let release: (() => Promise<void>) | null = null;

        try {
            // Parse the request body
            const requestData = testData;
            const { 
                labels, 
                templateType, 
                dimensions, 
                barcodeSource = 'recipient_name' 
            } = requestData;

            // Validate required fields
            if (!labels || !Array.isArray(labels) || labels.length === 0) {
                return new Response('Invalid or missing labels data', { status: 400 });
            }
            if (!templateType) {
                return new Response('Missing template type', { status: 400 });
            }
            if (!dimensions || !dimensions.width || !dimensions.height || !dimensions.unit) {
                return new Response('Invalid dimensions data', { status: 400 });
            }

            console.log(`Processing ${labels.length} labels with template: ${templateType}`);

            // Get browser and page from pool
            const { browser, page, release: releaseFunc } = await browserPool.getBrowser(env);
            release = releaseFunc;

            // Optimize page for PDF generation
            await page.setViewport({ width: 1200, height: 800 });
            
            // Disable images and unnecessary resources for faster loading (optional)
            // await page.setRequestInterception(true);
            // page.on('request', (req) => {
            // 	if (req.resourceType() === 'image' && !req.url().includes('data:')) {
            // 		req.abort();
            // 	} else {
            // 		req.continue();
            // 	}
            // });

            // Construct the URL for the print route
            const printUrl = new URL('http://localhost:5174/print-labels');
            printUrl.searchParams.set('templateType', templateType);
            printUrl.searchParams.set('dimensions', JSON.stringify(dimensions));
            printUrl.searchParams.set('labels', JSON.stringify(labels));
            printUrl.searchParams.set('barcodeSource', barcodeSource);

            console.log('Navigating to print route...');

            // Navigate to the print route
            await page.goto(printUrl.toString(), {
                waitUntil: 'networkidle0',
                timeout: 60000
            });

            // Wait for dynamic content (QR codes, barcodes) - scale with label count
            const waitTime = Math.min(2000 + (labels.length * 100), 10000); // Max 10 seconds
            await page.waitForTimeout(waitTime);

            // Convert dimensions
            const convertDimension = (value: number, unit: string): string => {
                switch (unit.toLowerCase()) {
                    case 'in': return `${value}in`;
                    case 'mm': return `${value}mm`;
                    case 'cm': return `${value}cm`;
                    case 'px': return `${value}px`;
                    default: return `${value}px`;
                }
            };

            const pdfWidth = convertDimension(dimensions.width, dimensions.unit);
            const pdfHeight = convertDimension(dimensions.height, dimensions.unit);

            console.log('Generating PDF...');

            // Generate PDF
            const pdf = await page.pdf({
                width: pdfWidth,
                height: pdfHeight,
                margin: { top: '0px', right: '0px', bottom: '0px', left: '0px' },
                printBackground: true,
                preferCSSPageSize: false
            });

            console.log(`PDF generated successfully: ${pdf.length} bytes for ${labels.length} labels`);

            // Return the PDF
            return new Response(pdf, {
                headers: {
                    "Content-Type": "application/pdf",
                    "Content-Disposition": `attachment; filename="labels_${new Date().toISOString().slice(0, 10)}.pdf"`,
                    "Content-Length": pdf.length.toString(),
                },
            });

        } catch (error:any) {
            console.error('PDF generation error:', error);
            return new Response(`PDF generation failed: ${error.message}`, { 
                status: 500,
                headers: { "Content-Type": "text/plain" }
            });

        } finally {
            // Always release the page back to the pool
            if (release) {
                await release();
            }
        }
    },
};